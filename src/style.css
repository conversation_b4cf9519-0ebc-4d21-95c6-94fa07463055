:root {
    --ap-body-background-color: #f5f5f7;
    --ap-section-bg-color: #fff;
    --ap-bg-color: var(--color-gray6);
    --ap-fill-color: var(--color-gray5);
    --ap-widget-frame-border-color: var(--color-gray5);
    --ap-widget-frame-bg-color: var(--color-gray6);
    --ap-widget-frame-active-border-color: var(--color-blue4);
    --ap-widget-frame-active-bg-color: var(--color-blue5);
    --ap-enabled-color: var(--color-green);
    --ap-disabled-color: var(--color-red);
    --ap-cli-background: rgb(0, 0, 0);
    --ap-info: var(--color-gray2);
    --ap-browser-height: 25em;
    --annot-line-color: var(--color-gray5);
    --annot-plug-color: var(--color-gray4);
}

@media (prefers-color-scheme: dark) {
    :root {
        --ap-body-background-color: #1d1d1f;
        --ap-section-bg-color: #000;
        --ap-bg-color: var(--color-gray3);
        --ap-fill-color: var(--color-gray2);
        --ap-widget-frame-border-color: var(--color-gray7);
        --ap-widget-frame-bg-color: var(--color-gray5);
        --ap-widget-frame-active-border-color: rgb(64, 156, 255);
        --ap-widget-frame-active-bg-color: rgb(37, 42, 46);
        --ap-cli-background: var(--color-gray5);
        --ap-info: var(--color-gray1);
        --annot-line-color: var(--color-gray4);
        --annot-plug-color: var(--color-gray3);
    }
}

table+* {
    margin-top: 1em;
}

table {
    margin-top: 1.5em;
}

td, th {
    border-width: 2px 0;
}

body {
    max-width: 60em;
    margin: auto;
    padding: 2em 2em 5em;
}

@media only screen and (max-width: 600px) {
    body {
        padding-left: 1.5em;
        padding-right: 1.5em;
    }
}

@media only screen and (max-width: 400px) {
    body {
        padding: 1.5em 1em 2em;
    }
}

var {
    font-style: normal;
}

header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: flex-start; /* to make request button respect its height */
    margin-bottom: 2em;
}

header h1::before {
    position: absolute;
    top: .05em;
    left: -1.5em;
    content: "";
    background: url("https://cdn.appio.so/app/appio.so/logo.png");
    background-size: contain;
    display: block;
    width: 1em;
    height: 1em;
}

@media only screen and (max-width: 1150px) {
    header h1 {
        margin-left: 1.4em;
    }
    header h1::before {
        left: 0;
    }
}

@media only screen and (max-width: 400px) {
    header h1 {
        font-size: 2.6em;
    }
}

.sign-up {
    padding: 1em;
}
.sign-up a {
    margin-top: .8em;
    background: var(--color-gray10);
    color: var(--ap-body-background-color);
    text-decoration: none;
    padding: 0.626em 1.5em;
    border-radius: 0.5em;
    display: inline-block;
    font-weight: 600;
}

.sign-up a:hover {
    background: var(--color-gray8);
}

.sign-up a:active {
    background: var(--color-gray11);
}

p + h4 {
    margin-top: 1em;
}

.section {
    background: var(--ap-section-bg-color);
    border-radius: 1em;
    padding: 3em;
    margin-top: 1.5em;
}

@media only screen and (max-width: 600px) {
    .section {
        padding: 2em;
    }
}

.section img {
    margin-bottom: 1em;
}

.banner-warning {
    color: var(--color-gray1);
    margin-top: 1.5em;
}

.banner-warning::before {
    content: "";
    background: url("/assets/warning.svg") no-repeat center/contain;
    width: 1em;
    height: 1em;
    display: inline-block;
    margin-right: .3em;
    vertical-align: text-top;
}

.browser {
    margin: 2em 0;
}

.browser-tabs label::before {
    content: "";
    background-size: contain;
}

.browser-tabs label::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    height: 80%;
    border-right: solid 1px var(--bw-tab-hover-bg-color);
}
.browser-tabs label:last-child::after {
    border-right: none;
}

.browser-tabs label[for="app"] {
    --app-icon: url("/assets/rocket.png");
}

.browser-tabs label[for="app"]::before {
    background-image: var(--app-icon);
}

.browser-tabs label[for="appio"]::before {
    background-image: url("https://demo.appio.so/favicon.ico");
}

.browser-tabs label[for="cli"]::before {
    background-image: url("https://demo.appio.so/cli.ico");
}

#app:checked ~ .browser-toolbar label[for="app"],
#appio:checked ~ .browser-toolbar label[for="appio"],
#cli:checked ~ .browser-toolbar label[for="cli"] {
    background-color: var(--bw-tab-active-bg-color);
}

#app:checked ~ .browser-toolbar label[for="app"]::after,
#appio:checked ~ .browser-toolbar label[for="appio"]::after,
#cli:checked ~ .browser-toolbar label[for="cli"]::after {
    border-right: none;
}

#app:checked ~ .browser-window [data-tab="app"],
#appio:checked ~ .browser-window [data-tab="appio"],
#cli:checked ~ .browser-window [data-tab="cli"]
{
    display: grid;
}

.browser-window {
    min-height: var(--ap-browser-height);
}



/***** App Tab *****/

[data-tab="app"] {
    grid-template-columns: 10em 1fr;
}

[data-tab="app"] nav {
    padding: 1em;
    display: flex;
    flex-direction: column;
    gap: .8em;
    background: var(--ap-bg-color);
    border-radius: var(--bw-radius);
}

[data-tab="app"] nav svg {
    width: 50%;
    fill: var(--ap-fill-color);
    margin-bottom: 1em;
}

[data-tab="app"] span {
    display: block;
    height: 1.2em;
    width: 100%;
    background: var(--ap-fill-color);
    border-radius: var(--bw-radius);
}
[data-tab="app"] span:nth-child(2n) {
    width: 80%;
}
[data-tab="app"] span:nth-child(3n) {
    width: 95%;
}
[data-tab="app"] span:nth-child(4n) {
    width: 90%;
}

[data-tab="app"] section {
    padding: 1em 2em;
}
[data-tab="app"] section h1 {
    font-size: 2.5em;
    color: var(--ap-fill-color);
}
[data-tab="app"] section > div {
    padding: 1.5em;
    margin-top: 1.5em;
    background: var(--ap-bg-color);
    border-radius: var(--bw-radius);
    display: flex;
    flex-direction: column;
    gap: 1em;
    align-items: flex-start;
    max-width: 25em;
}
[data-tab="app"] section span {
    height: 2em;
}
[data-tab="app"] section a.connect-button {
    display: inline-block;
    background: var(--color-blue4);
    color: white;
    text-transform: uppercase;
    padding: 1em 1.35em;
    line-height: 1;
    margin: .5em 0;
    font-weight: 600;
    border-radius: 9em;
    text-decoration: none;
}
[data-tab="app"] section a.connect-button:hover {
    background: var(--color-blue3);
}
[data-tab="app"] section a.connect-button:active {
    background: var(--color-blue2);
}

[data-tab="app"] .install-code {
    color: var(--color-gray1);
}

@media only screen and (max-width: 600px) {
    header {
        display: block;
    }
    [data-tab="app"] {
        grid-template-columns: 1fr;
    }
    [data-tab="app"] nav {
        display: none;
    }
    [data-tab="app"] section {
        padding: 1em;
    }
}



/***** Appio Tab *****/

[data-tab="appio"] {
    position: relative;
    overflow: hidden;
}

[data-tab="appio"] input[type="radio"], [data-tab="appio"] {
    display: none;
}

[data-tab="appio"] input[type="number"],
[data-tab="appio"] input[type="text"],
[data-tab="appio"] input[type="url"],
[data-tab="appio"] textarea
{
    max-width: 20em;
    font-size: 1.5em;
}

[data-tab="appio"] input[type="number"] {
    text-align: center;
}

[data-tab="appio"] textarea {
    height: 4.7em;
}

[data-tab="appio"] nav {
    display: flex;
    gap: 1em;
    background: var(--ap-bg-color);
    border-radius: var(--bw-radius);
    padding: .5em;
}

[data-tab="appio"] nav label {
    cursor: pointer;
    padding: .5em 1em;
    border-radius: var(--bw-radius);
    font-weight: 500;
    font-size: .8em;
    user-select: none;
}

[data-tab="appio"] section {
    display: none;
    width: 100%;
    margin-top: 1em;
    padding: .5em;
    overflow: hidden;
}

#users:checked ~ nav label[for="users"],
#widgets:checked ~ nav label[for="widgets"],
#notifications:checked ~ nav label[for="notifications"] {
    background: var(--ap-fill-color);
}

#users:checked ~ [data-tab="users"],
#widgets:checked ~ [data-tab="widgets"],
#notifications:checked ~ [data-tab="notifications"]
{
    display: block;
}

[data-tab="appio"] h1 {
    color: var(--ap-fill-color);
    font-size: 2.5em;
}

[data-tab="appio"] h2 {
    color: var(--ap-fill-color);
    font-size: 1.75em;
}

[data-tab="appio"] th {
    border-color: var(--ap-fill-color);
}

[data-tab="appio"] td {
    border-color: var(--ap-fill-color);
}

[data-tab="appio"] td.enabled::before {
    content: "✓";
    margin-right: .3em;
    color: var(--ap-enabled-color);
}

[data-tab="appio"] td.disabled::before {
    content: "✗";
    margin-right: .3em;
    color: var(--ap-disabled-color);
}



/***** Appio Nav *****/

#appio-nav {
    display: none;
}

[data-tab="appio"] > label[for="appio-nav"] {
    cursor: pointer;
    padding: .5em 1em;
    border-radius: var(--bw-radius);
    font-weight: 500;
    font-size: .8em;
    user-select: none;
    display: none;
    width: 6em;
    height: 33px;
    position: relative;
    background: var(--ap-fill-color);
    text-indent: 1.3em;
}

@media only screen and (max-width: 430px) {
    [data-tab="appio"] > label[for="appio-nav"] {
        display: block;
        z-index: 1001; /* position navigation opener above nav so we can close nav */
    }
}

[data-tab="appio"] > label[for="appio-nav"]::before,
[data-tab="appio"] > label[for="appio-nav"]::after {
    content: "";
    position: absolute;
    width: 1em;
    left: .7em;
    height: 1px;
    background: var(--color-gray11);
    transition: all 0.3s ease;
    border-radius: 1em;
}

[data-tab="appio"] > label[for="appio-nav"]::before {
    top: 13px;
}

[data-tab="appio"] > label[for="appio-nav"]::after {
    bottom: 13px;
}

[data-tab="appio"] #appio-nav:checked + label::before {
    transform: rotate(-45deg);
    top: 16px;
}

[data-tab="appio"] #appio-nav:checked + label::after {
    transform: rotate(45deg);
    bottom: 16px;
}

@media only screen and (max-width: 430px) {
    [data-tab="appio"] nav {
        overflow: hidden;
        max-height: 0;
        transition: all 0.3s ease;
        opacity: 0;
        position: absolute;
        top: -1em; /* to hide top rounded corners */
        left: 0;
        width: 100%;
        padding: 0 1em .5em;
        flex-direction: column;
        gap: .5em;
        z-index: 1000; /* position above all */
        border-bottom: solid 1px var(--bw-border-color);
    }
}

[data-tab="appio"] #appio-nav:checked ~ nav {
    max-height: 13em;
    opacity: 1;
    padding-top: 4.5em;
}

[data-tab="appio"] .info {
    color: var(--ap-info);
}

[data-tab="appio"] .no-users {
    color: var(--ap-info);
    line-height: 1.6;
    margin-bottom: 2em;
}

[data-tab="appio"] .no-users svg {
    fill: var(--ap-info);
    width: 1.75em;
    vertical-align: middle;
}


/***** Appio Users *****/

[data-tab="appio"] [data-tab="users"] [data-platform]::before {
    content: "";
    display: inline-block;
    width: 1em;
    height: 1em;
    margin-right: .5em;
    background: no-repeat center/contain;
    vertical-align: text-top;
}
[data-tab="appio"] [data-tab="users"] [data-platform="ios"]::before {
    background-image: url("/assets/platform_ios.svg");
}
[data-tab="appio"] [data-tab="users"] [data-platform="android"]::before {
    background-image: url("/assets/platform_android.svg");
}


/***** Appio Widgets *****/

[data-tab="appio"] [data-tab="widgets"] form {
    max-width: 20em;
}

[data-tab="appio"] [data-tab="widgets"] .widgets-template {
    margin: 1.5em 0 1em;
    display: grid;
    gap: 1em;
    grid-template: 1fr / 1fr 1fr;
}

[data-tab="appio"] [data-tab="widgets"] .widgets-template label {
    text-align: center;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

[data-tab="appio"] [data-tab="widgets"] .widgets-template label span {
    display: inline-block;
    margin-top: .5em;
    border-radius: 9em;
    padding: .3em 1em;
}

[data-tab="appio"] [data-tab="widgets"] .widgets-design {
    margin-bottom: 1.5em;
}

[data-tab="appio"] [data-tab="widgets"] .widgets-data {
    margin-bottom: .25em;
    display: grid;
    gap: 1em;
    grid-template: 1fr / 1fr 1fr;
}

[data-tab="appio"] [data-tab="widgets"] .widgets-data label {
    border: solid 4px var(--ap-widget-frame-border-color);
    border-radius: calc(var(--ap-border-radius) + 4px);
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    cursor: pointer;
    background: var(--ap-widget-frame-bg-color);
}

[data-tab="appio"] [data-tab="widgets"] .widgets-data input {
    margin-top: 0;
    background: var(--ap-widget-frame-bg-color);
}

[data-tab="appio"] [data-tab="widgets"] button + span {
    margin-left: .5em;
    color: var(--color-green);
}
[data-tab="appio"] [data-tab="widgets"] button + span.error {
    color: var(--color-red);
}

#widget-number:checked ~ label[for="widget-number"] span,
#widget-ring:checked ~ label[for="widget-ring"] span,
#widget-chart:checked ~ label[for="widget-chart"] span {
    background: var(--ap-widget-frame-active-border-color);
    color: #fff;
    font-weight: 500;
}

[data-tab="appio"] [data-tab="widgets"] .widget-frame {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 5em;
    height: 5em;
    margin: auto;
    border: solid 4px var(--ap-widget-frame-border-color);
    border-radius: 1em;
    background: var(--ap-widget-frame-bg-color);
}

#widget-number:checked ~ label[for="widget-number"] .widget-frame,
#widget-ring:checked ~ label[for="widget-ring"] .widget-frame,
#widget-chart:checked ~ label[for="widget-chart"] .widget-frame {
    border-color: var(--ap-widget-frame-active-border-color);
    background: var(--ap-widget-frame-active-bg-color);
}

.widget-number {
    font-size: 2em;
    font-weight: 600;
}

.widget-ring {
    font-size: 1.2em;
    font-weight: 600;
}

.widget-ring svg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    transform: rotate(126deg);
}

.widget-chart {
    width: 70%;
    height: 50%;
}

#widget-data-static:checked ~ label[for="widget-data-static"],
#widget-data-random:checked ~ label[for="widget-data-random"] {
    border-color: var(--ap-widget-frame-active-border-color);
    background: var(--ap-widget-frame-active-bg-color);
}

#widget-data-static:checked ~ label input {
    background: var(--ap-widget-frame-active-bg-color);
}


/***** Appio Notifications *****/

[data-tab="appio"] [data-tab="notifications"] button + span {
    margin-left: .5em;
    color: var(--color-green);
}

[data-tab="appio"] [data-tab="notifications"] button + span.error {
    color: var(--color-red);
}

[data-tab="appio"] [data-tab="notifications"] form {
    max-width: 20em;
}



/***** CLI Tab *****/

[data-tab="cli"] {
    background: var(--ap-cli-background);
    color: #999;
    font-family: "SF Mono",SFMono-Regular,ui-monospace,Menlo,monospace;
    letter-spacing: 0;
    overflow: scroll;
    scrollbar-width: thin;
    scrollbar-color: #ccc var(--ap-cli-background);
    overscroll-behavior: none;
    height: var(--ap-browser-height);
}

[data-tab="cli"] pre {
    font-family: "SF Mono",SFMono-Regular,ui-monospace,Menlo,monospace;
    letter-spacing: 0;
    font-size: .99em; /* to fit curl line with auth token without horizontal scrollbar */
}

[data-tab="cli"] i {
    font-style: normal;
    color: #ff3b30;
}

[data-tab="cli"] pre a {
    color: #ccc;
}

[data-tab="cli"] .cli-cmd-ok {
    color: #34e2e2;
}

[data-tab="cli"] .cli-cmd-err {
    color: #ef2929;
}

[data-tab="cli"] .cli-input-bar {
    display: flex;
}

[data-tab="cli"] .cli-input-bar span {
    margin-right: .6em;
    color: #fff;
}

[data-tab="cli"] input {
    flex: 2;
    border: 0;
    background: transparent;
    color: #fff;
    outline: none;
    font-family: "SF Mono",SFMono-Regular,ui-monospace,Menlo,monospace;
    letter-spacing: 0;
    font-size: 1em;
}

[data-tab="cli"] input::placeholder {
    color: #999; /* same as [data-tab="cli"] */
}

[data-tab="cli"]:hover input {
    background: #222;
    border-radius: .25em;
    cursor: pointer;
}

[data-tab="cli"] input:focus {
    background: transparent;
    cursor: text;
}

.session-btn {
    position: relative;
}
.session-btn::before {
    content: attr(data-confirmation);
    position: absolute;
    top: -2.35em;
    right: -.65em; /* better to use right instead of left due to text folding on small devices */
    white-space: nowrap;
    font-size: .7em;
    color: var(--ap-body-text-color);
    background: var(--ap-fill-color);
    padding: .4em 0; /* can't use horizontal padding because the tooltip would be always visible */
    border-radius: var(--bw-radius);
}

footer {
    margin-top: 2em;
}

footer div {
    margin-top: .75em;
    padding-top: .75em;
    border-top: solid 1px var(--color-gray5);
}
footer div span {
    margin-right: .5em;
    padding-right: .7em;
    border-right: solid 1px var(--color-gray5);
}
footer div span:last-child {
    border-right: none;
}

label:has(input[type="checkbox"]) {
    margin-top: .4em;
}



/***** Annotations *****/

@font-face {
    font-family: "Indie Flower";
    src: url("/assets/fonts/Indie_Flower/IndieFlower-Regular.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
}

#annotations {
    font-family: "Indie Flower", cursive;
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding-bottom: 1em;
}

#annotations div {
    font-size: 1.5em;
    text-align: center;
    color: var(--ap-info);
}

#annotations div:nth-of-type(1) {
    transform: rotate(-3deg);
}
#annotations div:nth-of-type(2) {
    transform: rotate(1deg);
}
#annotations div:nth-of-type(3) {
    transform: rotate(5deg);
}

@media only screen and (max-width: 600px) {
    #annotations div {
        font-size: 1em;
    }
}