window.addEventListener("load", () => {
    if (typeof LeaderLine !== "function") {
        return
    }

    setTimeout(() => {
        ["app", "appio", "cli"].forEach(id => {
            /* eslint-disable-next-line no-undef */
            new LeaderLine(
                document.getElementById(`annotation-${id}`),
                document.querySelector(`label[for=${id}]`),
                {
                    startSocketGravity: 40, // id === "app" ? 40 : "auto",
                    endSocketGravity: 40, // id === "app" ? 40 : "auto",
                    color: "var(--annot-line-color)",
                    endPlugColor: "var(--annot-plug-color)",
                    size: 3,
                    startSocket: "bottom",
                    endSocket: "top",
                    dash: true,
                }
            );
        })
    }, 100);
});