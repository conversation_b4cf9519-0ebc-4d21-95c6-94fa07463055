<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="utf-8">
    <title>Appio Demo</title>
    <link rel="canonical" href="https://demo.appio.so">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <!-- Sentry -->
    <script src="https://js-de.sentry-cdn.com/05b59c0f63ed916d9deebc51defe534b.min.js" crossorigin="anonymous"></script>
    <script>
        window.Sentry && Sentry.onLoad(function() {
            Sentry.init({
                replaysSessionSampleRate: 0.0, // This sets the sample rate. You may want to change it to 100% while in development and then sample at a lower rate in production.
                replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
                ignoreErrors: ["posthog"],
                allowUrls: [/https:\/\/((cdn)\.)?appio\.so/],
                // beforeSend(event, hint) {
                //     const error = hint.originalException;
                //     const msg = (event && event.message) || (error && error.message) || "";
                //
                //     // Drop Amplitude Session Replay IndexedDB warnings as a group
                //     if (/Amplitude Logger/i.test(msg)) {
                //         return null;
                //     }
                //
                //     // Exclude PostHog-specific errors
                //     if (error &&
                //         typeof error === "object" &&
                //         error.message &&
                //         error.message.includes("Object Not Found Matching Id") &&
                //         error.MethodName === "update"
                //     ) {
                //         return null; // Drop this error
                //     }
                //
                //     // Exclude anything mentioning PostHog
                //     if (JSON.stringify(event).includes("posthog")) {
                //         return null;
                //     }
                //
                //     return event; // Allow all other events
                // }
            })
        })
    </script>

    <!-- Icons -->
    <link rel="icon" id="favicon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open graph -->
    <meta property="og:url" content="https://demo.appio.so">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Appio Demo">
    <meta property="og:description" content="Appio is the fastest way to add push notifications and mobile widgets to your online service.">
    <meta property="og:image" content="/assets/social.png">

    <!-- Twitter -->
    <meta name="twitter:url" content="https://demo.appio.so">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@appio_so">
    <meta name="twitter:creator" content="@appio_so">
    <meta name="twitter:title" content="Appio Demo">
    <meta name="twitter:description" content="Appio is the fastest way to add push notifications and mobile widgets to your online service.">
    <meta name="twitter:image" content="/assets/social.png">
    <meta name="twitter:image:src" content="/assets/social.png">

    <!-- Amplitude -->
    <script src="https://cdn.amplitude.com/libs/analytics-browser-2.11.1-min.js.gz"></script>
    <script src="https://cdn.amplitude.com/libs/plugin-session-replay-browser-1.8.0-min.js.gz"></script>
    <script>
        if (window.amplitude && typeof window.amplitude.add === "function" && typeof window.amplitude.init === "function") {
            window.amplitude.add(window.sessionReplay.plugin({
                sampleRate: 1
            }));
            window.amplitude.init('422a7294588e2ed56224dc2050be87ca', {
                "autocapture": {
                    "elementInteractions": true
                }
            });
        }
    </script>

    <script src="https://cdn.appio.so/v1/appio.js"></script>
    <link rel="stylesheet" href="https://cdn.appio.so/appio.css">
    <link rel="stylesheet" href="/style.css?{{version}}">
    <link rel="preload" href="/assets/fonts/Indie_Flower/IndieFlower-Regular.ttf" as="font" type="font/ttf" crossorigin>
</head>
<body>

    <header>
        <h1>Appio demo</h1>
    </header>

    <!-- Or <a href="#video">watch the video</a> -->

    <div id="annotations">
        <div id="annotation-app">What your users see</div>
        <div id="annotation-appio">Your Appio dashboard</div>
        <div id="annotation-cli">For your developers</div>
    </div>

    <div id="browser" class="browser">
        <input type="radio" name="browser-tabs" id="app" checked>
        <input type="radio" name="browser-tabs" id="appio">
        <input type="radio" name="browser-tabs" id="cli">

        <div class="browser-toolbar">
            <div class="browser-tabs three">
                <label for="app"><var data-replace="service-title">Your App</var></label>
                <label for="appio">Appio</label>
                <label for="cli">CLI</label>
            </div>
        </div>
        <div class="browser-window">
            <div data-tab="app">
                <nav>
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="50" r="50" />
                        <clipPath id="clipRound">
                            <rect x="0" y="0" width="100" height="100" rx="20" ry="20" />
                        </clipPath>
                        <image clip-path="url(#clipRound)" id="app-profile-logo" href="/assets/rocket.png" x="0" y="0" height="100" width="100" />
                    </svg>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                </nav>
                <section>
                    <h1><var data-replace="service-title">Your App</var>'s Settings</h1>

                    <div>
                        <span></span>
                        <span></span>

                        <script inline="true" src="/scripts/ulid-2.3.0.min.js"></script>
                        <script inline="true" src="/scripts/appio-init.js"></script>
                        <a href="#" class="connect-button" onclick="clickOpenApp();return false">Download App</a>

                        <div class="install-code">
                            Show the <a href="https://docs.appio.so/#getting-started" id="install-code-btn" target="_blank">code snippet</a>
                        </div>
                        
                        <span></span>
                    </div>
                </section>
            </div>
            <div data-tab="appio">
                <input type="radio" name="appio-tabs" id="users" checked>
                <input type="radio" name="appio-tabs" id="widgets">
                <input type="radio" name="appio-tabs" id="notifications">

                <input type="checkbox" id="appio-nav">
                <label for="appio-nav">Menu</label>
                <nav>
                    <label for="users">Users</label>
                    <label for="widgets">Widgets</label>
                    <label for="notifications">Notifications</label>
                </nav>
                <script inline="true" src="/scripts/appio-close-menu.js"></script>

                <section data-tab="users">
                    <h1>Users</h1>
                    <p class="info">List of <b>your</b> connected users.</p>
                    <p class="no-users" id="appio-users-no-data">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29.502 26.1036">
                          <path d="M12.57 15.3865C12.1665 15.7176 11.807 16.0741 11.4975 16.4484C10.5837 15.9743 9.50926 15.6934 8.37891 15.6934C5.18555 15.6934 2.42188 17.9492 2.42188 20.3711C2.42188 20.5664 2.51953 20.6446 2.71484 20.6446L9.78046 20.6446C9.79511 21.1861 9.93586 21.6777 10.2158 22.0899L2.95898 22.0899C1.63086 22.0899 0.966797 21.5528 0.966797 20.459C0.966797 17.2168 4.28711 14.2481 8.37891 14.2481C9.93652 14.2481 11.38 14.6762 12.57 15.3865ZM6.90435 7.30365C6.46168 7.75393 6.18164 8.40434 6.18164 9.13088C6.18164 10.5371 7.19727 11.6797 8.38867 11.6797C9.12378 11.6797 9.7894 11.2518 10.1939 10.5917L11.2354 11.6327C10.5636 12.5484 9.53477 13.1348 8.38867 13.1348C6.37695 13.1348 4.72656 11.3379 4.72656 9.13088C4.72656 8.01231 5.16989 6.99605 5.87945 6.27923Z"/>
                          <path d="M16.0746 16.4696C13.8609 17.4563 12.6758 19.228 12.6758 20.3418C12.6758 20.5469 12.7832 20.6153 13.0371 20.6153L20.2223 20.6153L21.6976 22.0899L13.5059 22.0899C11.875 22.0899 11.1035 21.6016 11.1035 20.5274C11.1035 18.8431 12.5042 16.6637 14.9814 15.3768ZM28.1641 20.5274C28.1641 21.296 27.7691 21.7647 26.948 21.9676L25.5957 20.6153L26.2402 20.6153C26.4941 20.6153 26.5918 20.5469 26.5918 20.3418C26.5918 18.8767 24.5411 16.2732 20.8007 15.8203L19.2643 14.2839C19.384 14.2698 19.5107 14.2676 19.6387 14.2676C24.9316 14.2676 28.1641 17.9688 28.1641 20.5274ZM23.8574 8.46682C23.8574 10.9864 21.9629 13.0469 19.6387 13.0469C18.6761 13.0469 17.7844 12.6922 17.074 12.0935L16.5179 11.5375C15.8355 10.7312 15.4199 9.65885 15.4199 8.48635C15.4199 6.00588 17.3242 4.00393 19.6387 4.00393C21.9727 4.00393 23.8574 5.96682 23.8574 8.46682ZM16.9922 8.48635C16.9922 10.2149 18.2129 11.5723 19.6387 11.5723C21.084 11.5723 22.2852 10.1953 22.2852 8.46682C22.2852 6.75784 21.0938 5.47854 19.6387 5.47854C18.1934 5.47854 16.9922 6.78713 16.9922 8.48635Z"/>
                          <path d="M25.9766 24.7461C26.2695 25.0391 26.7578 25.0391 27.041 24.7461C27.3242 24.4434 27.3438 23.9844 27.041 23.6817L6.42578 3.06643C6.12305 2.7637 5.6543 2.77346 5.35156 3.06643C5.06836 3.34963 5.06836 3.84768 5.35156 4.13088Z"/>
                        </svg>
                        No users connected yet.<br>
                        To connect a user, go to the first tab and click <a href="#app">Download App</a>.
                    </p>

                    <table style="display:none;" id="appio-users-data">
                        <thead>
                            <tr>
                                <th>Device</th>
                                <th>Notifications</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </section>
                <section data-tab="widgets">
                    <h1>Widgets</h1>
                    <p class="info">
                        Design widget for <b>your</b> connected users to use on their phones.<br>
                        Appio offers a powerful editor to create custom widgets that match your brand.<br>
                        This is a simplified version.
                    </p>
                    <form id="appio-widgets-form">
                        <h2>Template</h2>
                        <div class="widgets-template">
                            <input type="radio" name="appio-widget-template" id="widget-number" value="number">
                            <input type="radio" name="appio-widget-template" id="widget-ring" value="ring">
                            <!--<input type="radio" name="appio-widget-template" id="widget-chart" value="chart">-->

                            <label for="widget-number">
                                <div class="widget-frame">
                                    <div class="widget-number">
                                        <var>12</var>
                                    </div>
                                </div>
                                <span>Number</span>
                            </label>

                            <label for="widget-ring">
                                <div class="widget-frame">
                                    <div class="widget-ring">
                                        <svg width="50" height="50" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                                            <!-- background: 2πr and 80% cut at the bottom -->
                                            <circle cx="60" cy="60" r="40" stroke-width="11" fill="transparent" stroke="var(--color-gray1)" opacity="0.3" stroke-dasharray="251.3274122872" stroke-dashoffset="50.2654824574" stroke-linecap="round" />
                                            <!-- 65% progress -->
                                            <circle cx="60" cy="60" r="40" stroke-width="11" fill="transparent" stroke="var(--color-gray1)" stroke-dasharray="251.3274122872" stroke-dashoffset="87.9645943005" stroke-linecap="round" />
                                        </svg>
                                        <var>65</var>
                                    </div>
                                </div>
                                <span>Ring</span>
                            </label>

<!--                            <label for="widget-chart">-->
<!--                                <div class="widget-frame">-->
<!--                                    <div class="widget-chart">-->
<!--                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25">-->
<!--                                            <path d="M19.8633 18.3887L22.5 18.3887C24.0234 18.3887 24.8145 17.6562 24.8145 16.2109L24.8145 2.1875C24.8145 0.742188 24.0234 0 22.5 0L19.8633 0C18.3496 0 17.5684 0.742188 17.5684 2.1875L17.5684 16.2109C17.5684 17.6562 18.3496 18.3887 19.8633 18.3887ZM20.127 16.9141C19.3848 16.9141 19.1211 16.6602 19.1211 15.9277L19.1211 2.4707C19.1211 1.73828 19.3848 1.47461 20.127 1.47461L22.2461 1.47461C22.998 1.47461 23.2617 1.73828 23.2617 2.4707L23.2617 15.9277C23.2617 16.6602 22.998 16.9141 22.2461 16.9141Z" fill="var(&#45;&#45;color-gray11)" fill-opacity="0.85"/>-->
<!--                                            <path d="M11.084 18.3887L13.7207 18.3887C15.2441 18.3887 16.0254 17.6562 16.0254 16.2109L16.0254 5.03906C16.0254 3.59375 15.2441 2.85156 13.7207 2.85156L11.084 2.85156C9.56055 2.85156 8.7793 3.59375 8.7793 5.03906L8.7793 16.2109C8.7793 17.6562 9.56055 18.3887 11.084 18.3887ZM11.3477 16.9141C10.5957 16.9141 10.332 16.6602 10.332 15.9277L10.332 5.32227C10.332 4.58984 10.5957 4.32617 11.3477 4.32617L13.4668 4.32617C14.2188 4.32617 14.4727 4.58984 14.4727 5.32227L14.4727 15.9277C14.4727 16.6602 14.2188 16.9141 13.4668 16.9141Z" fill="var(&#45;&#45;color-gray11)" fill-opacity="0.85"/>-->
<!--                                            <path d="M2.30469 18.3887L4.93164 18.3887C6.45508 18.3887 7.24609 17.6562 7.24609 16.2109L7.24609 7.88086C7.24609 6.43555 6.45508 5.69336 4.93164 5.69336L2.30469 5.69336C0.78125 5.69336 0 6.43555 0 7.88086L0 16.2109C0 17.6562 0.78125 18.3887 2.30469 18.3887ZM2.55859 16.9141C1.81641 16.9141 1.55273 16.6602 1.55273 15.9277L1.55273 8.16406C1.55273 7.42188 1.81641 7.16797 2.55859 7.16797L4.67773 7.16797C5.42969 7.16797 5.69336 7.42188 5.69336 8.16406L5.69336 15.9277C5.69336 16.6602 5.42969 16.9141 4.67773 16.9141Z" fill="var(&#45;&#45;color-gray11)" fill-opacity="0.85"/>-->
<!--                                        </svg>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <span>Chart</span>-->
<!--                            </label>-->
                        </div>

                        <h2>Customize</h2>
                        <div class="widgets-design">
                            <label class="form-dropdown">
                                <span>Text color:</span>
                                <select name="appio-widget-text-color">
                                    <option value="primary">Primary</option>
                                    <option value="blue">Blue</option>
                                    <option value="cyan">Cyan</option>
                                    <option value="green">Green</option>
                                    <option value="orange">Orange</option>
                                    <option value="pink">Pink</option>
                                    <option value="purple">Purple</option>
                                    <option value="red">Red</option>
                                    <option value="teal">Teal</option>
                                    <option value="yellow">Yellow</option>
                                </select>
                            </label>
                            <label class="form-dropdown">
                                <span>Background color:</span>
                                <select name="appio-widget-background-color">
                                    <option value="primary">Primary</option>
                                    <option value="blue">Blue</option>
                                    <option value="cyan">Cyan</option>
                                    <option value="green">Green</option>
                                    <option value="orange">Orange</option>
                                    <option value="pink">Pink</option>
                                    <option value="purple">Purple</option>
                                    <option value="red">Red</option>
                                    <option value="teal">Teal</option>
                                    <option value="yellow">Yellow</option>
                                </select>
                            </label>
                            <label class="form-dropdown">
                                <span>Tint color:</span>
                                <select name="appio-widget-tint-color">
                                    <option value="primary">Primary</option>
                                    <option value="blue">Blue</option>
                                    <option value="cyan">Cyan</option>
                                    <option value="green">Green</option>
                                    <option value="orange">Orange</option>
                                    <option value="pink">Pink</option>
                                    <option value="purple">Purple</option>
                                    <option value="red">Red</option>
                                    <option value="teal">Teal</option>
                                    <option value="yellow">Yellow</option>
                                </select>
                            </label>
                        </div>

                        <h2>Data</h2>
                        <div class="widgets-data">
                            <input type="radio" name="appio-widget-data-type" id="widget-data-static" value="static">
                            <input type="radio" name="appio-widget-data-type" id="widget-data-random" value="random">

                            <label for="widget-data-static">
                                <input type="number" value="0" name="appio-widget-data-value">
                            </label>
                            <label for="widget-data-random">
                                Random<br>
                                number
                            </label>
                        </div>

                        <label>
                            <button>Update</button>
                            <span id="appio-widgets-confirmation"></span>
                        </label>
                    </form>
                </section>
                <section data-tab="notifications">
                    <h1>Notifications</h1>
                    <p class="info">Send push notifications to <b>your</b> connected users.</p>
                    <p class="no-users" id="appio-notifications-no-data">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29.502 26.1036">
                            <path d="M12.57 15.3865C12.1665 15.7176 11.807 16.0741 11.4975 16.4484C10.5837 15.9743 9.50926 15.6934 8.37891 15.6934C5.18555 15.6934 2.42188 17.9492 2.42188 20.3711C2.42188 20.5664 2.51953 20.6446 2.71484 20.6446L9.78046 20.6446C9.79511 21.1861 9.93586 21.6777 10.2158 22.0899L2.95898 22.0899C1.63086 22.0899 0.966797 21.5528 0.966797 20.459C0.966797 17.2168 4.28711 14.2481 8.37891 14.2481C9.93652 14.2481 11.38 14.6762 12.57 15.3865ZM6.90435 7.30365C6.46168 7.75393 6.18164 8.40434 6.18164 9.13088C6.18164 10.5371 7.19727 11.6797 8.38867 11.6797C9.12378 11.6797 9.7894 11.2518 10.1939 10.5917L11.2354 11.6327C10.5636 12.5484 9.53477 13.1348 8.38867 13.1348C6.37695 13.1348 4.72656 11.3379 4.72656 9.13088C4.72656 8.01231 5.16989 6.99605 5.87945 6.27923Z"/>
                            <path d="M16.0746 16.4696C13.8609 17.4563 12.6758 19.228 12.6758 20.3418C12.6758 20.5469 12.7832 20.6153 13.0371 20.6153L20.2223 20.6153L21.6976 22.0899L13.5059 22.0899C11.875 22.0899 11.1035 21.6016 11.1035 20.5274C11.1035 18.8431 12.5042 16.6637 14.9814 15.3768ZM28.1641 20.5274C28.1641 21.296 27.7691 21.7647 26.948 21.9676L25.5957 20.6153L26.2402 20.6153C26.4941 20.6153 26.5918 20.5469 26.5918 20.3418C26.5918 18.8767 24.5411 16.2732 20.8007 15.8203L19.2643 14.2839C19.384 14.2698 19.5107 14.2676 19.6387 14.2676C24.9316 14.2676 28.1641 17.9688 28.1641 20.5274ZM23.8574 8.46682C23.8574 10.9864 21.9629 13.0469 19.6387 13.0469C18.6761 13.0469 17.7844 12.6922 17.074 12.0935L16.5179 11.5375C15.8355 10.7312 15.4199 9.65885 15.4199 8.48635C15.4199 6.00588 17.3242 4.00393 19.6387 4.00393C21.9727 4.00393 23.8574 5.96682 23.8574 8.46682ZM16.9922 8.48635C16.9922 10.2149 18.2129 11.5723 19.6387 11.5723C21.084 11.5723 22.2852 10.1953 22.2852 8.46682C22.2852 6.75784 21.0938 5.47854 19.6387 5.47854C18.1934 5.47854 16.9922 6.78713 16.9922 8.48635Z"/>
                            <path d="M25.9766 24.7461C26.2695 25.0391 26.7578 25.0391 27.041 24.7461C27.3242 24.4434 27.3438 23.9844 27.041 23.6817L6.42578 3.06643C6.12305 2.7637 5.6543 2.77346 5.35156 3.06643C5.06836 3.34963 5.06836 3.84768 5.35156 4.13088Z"/>
                        </svg>
                        There are no connected users to receive notifications.<br>
                        To connect a user, go to the first tab and click <a href="#app">Download App</a>.
                    </p>
                    <form id="appio-notifications-form">
                        <label style="display:none;">
                            <input name="title" required placeholder="Title" value="Appio Demo" type="text">
                        </label>
                        <label>
                            <textarea name="message" required minlength="2" placeholder="Message‥"></textarea>
                        </label>
                        <label>
                            <input name="link" placeholder="Link" type="text" pattern="^(https?:\/\/)?[a-z\d.]*\.[a-z]{2,6}(\/.*)?$">
                        </label>
                        <label class="form-dropdown">
                            <span>Send:</span>
                            <select name="send-at">
                                <option value="now">Now</option>
                                <option value="5min">In 5 minutes</option>
                            </select>
                        </label>
                        <label>
                            <input type="checkbox" name="image">
                            <span></span>
                            Include demo image
                        </label>
                        <button>Send</button>
                        <span id="appio-notification-confirmation"></span>
                    </form>
                </section>
                <script inline="true" src="/scripts/appio-section.js"></script>
            </div>
            <div data-tab="cli">
                <div>
                    <pre id="cli-output"></pre>
                    <div class="cli-input-bar">
                        <span id="cli-prefix">➜</span>
                        <input id="cli-input" autocomplete="off" enterkeyhint="send">
                    </div>
                </div>
                <script inline="true" src="/scripts/cli-section.js"></script>
            </div>
        </div>
    </div>

    <div class="sign-up">
        <h4>Interested in Appio?</h4>
        <a href="https://appio.so/list/" target="_blank" class="sign-up">Get Started right now for free</a>
    </div>

    <div class="section">
        <img src="/assets/what-is-appio.svg" width="56" height="56" alt="What is Appio?">
        <h4>What is Appio?</h4>
        <p>
            <a href="https://appio.so" target="_blank">Appio</a> is the fastest way to add push notifications and mobile widgets to your online service. Without coding or maintaining mobile apps,
            hiring developers, or dealing with app stores.<br>
        </p>
    </div>

    <div class="section">
        <img src="/assets/how-appio-works.svg" width="56" height="56" alt="What is Appio?">
        <h4>How does it work?</h4>
        <ul>
            <li><a href="#app">Onboard users in seconds</a> – add a short code snippet to your web app</li>
            <li><a href="#appio/widgets">Design widgets</a> and <a href="#appio/notifications">send push notifications</a> – all from our simple web interface</li>
            <li><a href="#cli">Integrate with your workflows</a> – explore our API via an interactive CLI demo</li>
        </ul>
    </div>

    <footer>
        Testing Appio on multiple devices or with teammates? <a href="#" id="session-btn" class="session-btn">Share this session to link up</a>.
        <div>
            <span>Copyright © Appio Limited.</span>
            <span>Start using <a href="https://appio.so" target="_blank">Appio</a>.</span>
            <span>Explore the <a href="https://docs.appio.so" target="_blank">Documentation</a>.</span>
        </div>
    </footer>

    <script inline="true" src="/scripts/tabs.js"></script>

<!--    &lt;!&ndash; PostHog &ndash;&gt;-->
<!--    <script>-->
<!--        !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init me ws ys ps bs capture je Di ks register register_once register_for_session unregister unregister_for_session Ps getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Es $s createPersonProfile Is opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Ss debug xs getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);-->
<!--        posthog.init('phc_LBWGGE289NOf8baQoCIYP9l38yODewgjI2rZnkAfYYi', {-->
<!--            api_host:'https://eu.i.posthog.com',-->
<!--            person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well-->
<!--            autocapture: true,-->
<!--            capture_pageview: true,-->
<!--            cookie_domain: '.appio.so',-->
<!--            disable_surveys: true-->
<!--        })-->
<!--    </script>-->

    <!-- At the bottom to load this last -->s
    <script inline="true" src="/scripts/leader-line.min.js"></script>
    <script inline="true" src="/scripts/appio-annotations.js"></script>

</body>
</html>
