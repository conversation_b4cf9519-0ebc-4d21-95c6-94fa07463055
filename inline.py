# Inlines all scripts and styles in `dist` folder and deletes `dist/scripts` and `dist/styles` folders
# Run: python3 inline.py

import re
import sys
import shutil
import os

input_file = 'dist/index.html'
output_file = 'dist/index.html'
dist_dir = 'dist/'

def main():
    if not os.path.exists(input_file):
        print(f"🔴 File '{input_file}' does not exist")
        sys.exit()

    with open(input_file, "r") as f:
        html = f.read()

    html = scripts(html)
    html = styles(html)
    html = css(html)

    print(f"🟢 Inlined")

def scripts(html):
    # Inline scripts
    pattern = re.compile(r'<script inline="true" src="/?([^"]+)"></script>')
    new_html = pattern.sub(inline_script, html)

    with open(output_file, "w") as f:
        f.write(new_html)

    # Clean scripts folder
    delete_scripts_dir = dist_dir + 'scripts'
    if os.path.exists(delete_scripts_dir) and os.path.isdir(delete_scripts_dir):
        shutil.rmtree(delete_scripts_dir)

    return new_html

def styles(html):
    # Inline styles
    pattern = re.compile(r'<link inline="true" rel="stylesheet" href="/?([^"]+)">')
    new_html = pattern.sub(inline_style, html)

    with open(output_file, "w") as f:
        f.write(new_html)

    # Clean scripts folder
    delete_styles_dir = dist_dir + 'styles'
    if os.path.exists(delete_styles_dir) and os.path.isdir(delete_styles_dir):
        shutil.rmtree(delete_styles_dir)

    return new_html

def css(html):
    # Inject commit hash to reset css cache
    // TODO: replace `{{version}}` with the git commit hash
    pattern = re.compile(r'{{version}}')
    new_html = "new version"

    with open(output_file, "w") as f:
        f.write(new_html)

    return new_html

def inline_script(match):
    filename = dist_dir + match.group(1)
    try:
        with open(filename, "r") as f:
            contents = f.read()
    except Exception as e:
        sys.stderr.write(f"🔴 Error reading {filename}: {e}\n")
        contents = ""
    return f"<script>{contents}</script>"

def inline_style(match):
    filename = dist_dir + match.group(1)
    try:
        with open(filename, "r") as f:
            contents = f.read()
    except Exception as e:
        sys.stderr.write(f"🔴 Error reading {filename}: {e}\n")
        contents = ""
    return f"<style>{contents}</style>"

if __name__ == "__main__":
    main()
